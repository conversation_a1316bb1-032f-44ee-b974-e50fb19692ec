#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
generate_all_datasets.py

Unified script to generate problem instances for all task configurations:
- 2 robots, 30 tasks
- 2 robots, 50 tasks  
- 2 robots, 100 tasks

This script can generate datasets for all configurations sequentially or in parallel.
"""

import os
import sys
import argparse
import subprocess
import multiprocessing as mp
from datetime import datetime
from config import ConfigManager, get_all_task_configs


def generate_dataset_for_config(task_count, num_instances=1000):
    """Generate dataset for a specific task configuration"""
    print(f"\n🚀 Starting generation for {task_count} tasks configuration...")
    
    cmd = [
        sys.executable, "generate_saved_makespan.py",
        "--num-tasks", str(task_count),
        "--num-instances", str(num_instances)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ Successfully generated dataset for {task_count} tasks")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to generate dataset for {task_count} tasks")
        print(f"Error: {e.stderr}")
        return False


def generate_all_datasets_sequential(num_instances=1000):
    """Generate all datasets sequentially"""
    print("🔄 Generating datasets sequentially...")
    
    configs = get_all_task_configs()
    results = {}
    
    for task_count in sorted(configs.keys()):
        print(f"\n{'='*50}")
        print(f"Generating dataset for {task_count} tasks")
        print(f"{'='*50}")
        
        success = generate_dataset_for_config(task_count, num_instances)
        results[task_count] = success
    
    return results


def generate_all_datasets_parallel(num_instances=1000, max_workers=None):
    """Generate all datasets in parallel"""
    print("⚡ Generating datasets in parallel...")
    
    configs = get_all_task_configs()
    task_counts = sorted(configs.keys())
    
    if max_workers is None:
        max_workers = min(len(task_counts), mp.cpu_count())
    
    print(f"Using {max_workers} parallel workers")
    
    with mp.Pool(max_workers) as pool:
        # Create arguments for each task
        args = [(task_count, num_instances) for task_count in task_counts]
        
        # Run in parallel
        results = pool.starmap(generate_dataset_for_config, args)
    
    # Combine results
    return dict(zip(task_counts, results))


def ensure_all_directories():
    """Ensure all necessary directories exist for all configurations"""
    print("📁 Creating directories for all configurations...")
    
    configs = get_all_task_configs()
    for task_count, config in configs.items():
        print(f"  Creating directories for {task_count} tasks...")
        ConfigManager.ensure_directories(config)
    
    print("✅ All directories created")


def print_summary(results):
    """Print summary of generation results"""
    print(f"\n{'='*60}")
    print("📊 GENERATION SUMMARY")
    print(f"{'='*60}")
    
    total_configs = len(results)
    successful_configs = sum(1 for success in results.values() if success)
    
    for task_count, success in sorted(results.items()):
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"  {task_count:3d} tasks: {status}")
    
    print(f"\n📈 Overall: {successful_configs}/{total_configs} configurations successful")
    
    if successful_configs == total_configs:
        print("🎉 All datasets generated successfully!")
    else:
        print("⚠️  Some datasets failed to generate. Check logs above.")


def main():
    """Main function with command line argument support"""
    parser = argparse.ArgumentParser(
        description="Generate problem instances for all task configurations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate all datasets sequentially with default 1000 instances each
  python generate_all_datasets.py
  
  # Generate all datasets in parallel with 500 instances each
  python generate_all_datasets.py --parallel --num-instances 500
  
  # Generate only specific configurations
  python generate_all_datasets.py --tasks 30 50 --num-instances 100
        """
    )
    
    parser.add_argument("--num-instances", type=int, default=1000,
                       help="Number of instances to generate per configuration (default: 1000)")
    
    parser.add_argument("--parallel", action="store_true",
                       help="Generate datasets in parallel (default: sequential)")
    
    parser.add_argument("--max-workers", type=int, default=None,
                       help="Maximum number of parallel workers (default: auto)")
    
    parser.add_argument("--tasks", type=int, nargs="+", choices=[30, 50, 100],
                       help="Specific task counts to generate (default: all)")
    
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be generated without actually generating")
    
    args = parser.parse_args()
    
    start_time = datetime.now()
    
    print("🤖 Multi-Robot Task Scheduling Dataset Generator")
    print("=" * 60)
    print(f"📅 Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Determine which configurations to generate
    if args.tasks:
        task_counts = args.tasks
        print(f"🎯 Generating datasets for: {task_counts} tasks")
    else:
        configs = get_all_task_configs()
        task_counts = sorted(configs.keys())
        print(f"🎯 Generating datasets for all configurations: {task_counts} tasks")
    
    print(f"📊 Instances per configuration: {args.num_instances}")
    print(f"⚡ Mode: {'Parallel' if args.parallel else 'Sequential'}")
    
    if args.dry_run:
        print("\n🔍 DRY RUN - No actual generation will occur")
        for task_count in task_counts:
            config = get_all_task_configs()[task_count]
            print(f"  Would generate {args.num_instances} instances for {task_count} tasks")
            print(f"    Output folder: {config.data_folder}")
        return
    
    # Ensure directories exist
    ensure_all_directories()
    
    # Generate datasets
    if args.parallel:
        # Filter configs for parallel generation
        if args.tasks:
            # For specific tasks, generate them in parallel
            with mp.Pool(min(len(task_counts), args.max_workers or mp.cpu_count())) as pool:
                args_list = [(task_count, args.num_instances) for task_count in task_counts]
                results = pool.starmap(generate_dataset_for_config, args_list)
            results = dict(zip(task_counts, results))
        else:
            results = generate_all_datasets_parallel(args.num_instances, args.max_workers)
    else:
        # Sequential generation
        results = {}
        for task_count in task_counts:
            print(f"\n{'='*50}")
            print(f"Generating dataset for {task_count} tasks")
            print(f"{'='*50}")
            success = generate_dataset_for_config(task_count, args.num_instances)
            results[task_count] = success
    
    end_time = datetime.now()
    
    # Print summary
    print_summary(results)
    print(f"\n⏱️  Total time: {end_time - start_time}")
    print(f"🏁 Finished at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
