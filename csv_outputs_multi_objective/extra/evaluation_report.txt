MULTI-OBJECTIVE MRC SCHEDULING EVALUATION REPORT
============================================================

OVERALL STATISTICS
------------------------------
Total instances tested: 2000
Total models evaluated: 5
Total evaluations: 4060
Overall feasibility rate: 0.374

MODEL PERFORMANCE SUMMARY
------------------------------

Model: DecentralizedMultiObj
  Type: multi_objective
  Alpha (makespan weight): 0.300
  Beta (balance weight): 0.700
  Feasibility: 18/20 (0.900)
  Makespan: 14.88 ± 1.89 (range: 11.17-18.16)
  Workload Balance: 0.277 ± 0.098 (range: 0.104-0.485)
  Runtime: 4.898 ± 0.825 seconds

Model: EDF_Baseline
  Type: baseline
  Feasibility: 15/20 (0.750)
  Makespan: 18.13 ± 3.47 (range: 10.14-22.69)
  Workload Balance: 0.813 ± 0.207 (range: 0.557-1.243)
  Runtime: 1.996 ± 0.544 seconds

Model: Tercio_Baseline
  Type: baseline
  Feasibility: 16/20 (0.800)
  Makespan: 16.88 ± 2.82 (range: 13.12-22.48)
  Workload Balance: 0.589 ± 0.163 (range: 0.279-0.861)
  Runtime: 3.145 ± 0.697 seconds

Model: workload_balance_decentralized_a0.5_b0.5
  Type: multi_objective
  Feasibility: 734/2000 (0.367)
  Makespan: nan ± nan (range: nan-nan)
  Workload Balance: 1.425 ± 0.758 (range: 0.500-2.500)
  Runtime: 6.905 ± 0.223 seconds

Model: makespans_decentralized_a0.5_b0.5
  Type: multi_objective
  Feasibility: 734/2000 (0.367)
  Makespan: 27.97 ± 6.53 (range: 8.00-48.00)
  Workload Balance: nan ± nan (range: nan-nan)
  Runtime: 6.905 ± 0.223 seconds


BEST PERFORMERS
------------------------------
Best Makespan: DecentralizedMultiObj (14.88)
Best Workload Balance: DecentralizedMultiObj (0.277)
Best Feasibility: DecentralizedMultiObj (0.900)


RECOMMENDATIONS
------------------------------
Multi-objective models vs Baselines:
  Makespan: 21.42 vs 17.51 (worse)
  Balance: 0.851 vs 0.701 (worse)
