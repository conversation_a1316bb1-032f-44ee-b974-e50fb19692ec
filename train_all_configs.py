#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
train_all_configs.py

Unified training script for multi-robot task scheduling across different configurations:
- 2 robots, 30 tasks
- 2 robots, 50 tasks  
- 2 robots, 100 tasks

This script can train models for all configurations sequentially or in parallel.
"""

import os
import sys
import argparse
import subprocess
import multiprocessing as mp
from datetime import datetime
from config import ConfigManager, get_all_task_configs


def train_config(task_count, training_args):
    """Train model for a specific task configuration"""
    print(f"\n🚀 Starting training for {task_count} tasks configuration...")
    
    config = get_all_task_configs()[task_count]
    
    # Build command for decentralized multi-objective training
    cmd = [
        sys.executable, "decentralized_multi_objective_train.py",
        "--num-tasks", str(task_count),
        "--path-to-train", config.constraints_folder,
        "--train-start-no", str(config.train_start_no),
        "--train-end-no", str(config.train_end_no),
        "--cpsave", f"./cp_decentralized_{task_count}tasks",
        "--tbdir", f"./runs/DecentralizedMO_{task_count}tasks"
    ]
    
    # Add optional training arguments
    if training_args.steps:
        cmd.extend(["--steps", str(training_args.steps)])
    if training_args.batch_size:
        cmd.extend(["--batch-size", str(training_args.batch_size)])
    if training_args.lr:
        cmd.extend(["--lr", str(training_args.lr)])
    if training_args.weight_decay:
        cmd.extend(["--weight-decay", str(training_args.weight_decay)])
    if training_args.alpha:
        cmd.extend(["--alpha", str(training_args.alpha)])
    if training_args.beta:
        cmd.extend(["--beta", str(training_args.beta)])
    if training_args.device:
        cmd.extend(["--device", training_args.device])
    if training_args.checkpoint_interval:
        cmd.extend(["--checkpoint-interval", str(training_args.checkpoint_interval)])
    
    try:
        print(f"📝 Command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ Successfully trained model for {task_count} tasks")
        if result.stdout:
            print(f"📊 Training output:\n{result.stdout[-500:]}")  # Last 500 chars
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to train model for {task_count} tasks")
        print(f"Error: {e.stderr}")
        return False


def train_all_configs_sequential(training_args):
    """Train all configurations sequentially"""
    print("🔄 Training models sequentially...")
    
    configs = get_all_task_configs()
    results = {}
    
    for task_count in sorted(configs.keys()):
        print(f"\n{'='*60}")
        print(f"Training model for {task_count} tasks")
        print(f"{'='*60}")
        
        success = train_config(task_count, training_args)
        results[task_count] = success
        
        if not success and training_args.stop_on_failure:
            print(f"⚠️  Stopping due to failure in {task_count} tasks configuration")
            break
    
    return results


def train_all_configs_parallel(training_args, max_workers=None):
    """Train all configurations in parallel"""
    print("⚡ Training models in parallel...")
    
    configs = get_all_task_configs()
    task_counts = sorted(configs.keys())
    
    if max_workers is None:
        max_workers = min(len(task_counts), mp.cpu_count())
    
    print(f"Using {max_workers} parallel workers")
    
    with mp.Pool(max_workers) as pool:
        # Create arguments for each task
        args = [(task_count, training_args) for task_count in task_counts]
        
        # Run in parallel
        results = pool.starmap(train_config, args)
    
    # Combine results
    return dict(zip(task_counts, results))


def ensure_all_directories():
    """Ensure all necessary directories exist for all configurations"""
    print("📁 Creating directories for all configurations...")
    
    configs = get_all_task_configs()
    for task_count, config in configs.items():
        print(f"  Creating directories for {task_count} tasks...")
        ConfigManager.ensure_directories(config)
    
    print("✅ All directories created")


def check_data_availability():
    """Check if training data is available for all configurations"""
    print("🔍 Checking data availability...")
    
    configs = get_all_task_configs()
    missing_data = []
    
    for task_count, config in configs.items():
        # Check if constraint files exist
        constraints_dir = config.constraints_folder
        if not os.path.exists(constraints_dir):
            missing_data.append(task_count)
            continue
        
        # Check if we have enough training instances
        expected_files = [f"{i:05d}_dur.txt" for i in range(1, min(11, config.train_end_no + 1))]
        existing_files = [f for f in expected_files if os.path.exists(os.path.join(constraints_dir, f))]
        
        if len(existing_files) < 5:  # Need at least 5 files for basic training
            missing_data.append(task_count)
    
    if missing_data:
        print(f"⚠️  Missing training data for configurations: {missing_data}")
        print("   Generate data first using:")
        for task_count in missing_data:
            print(f"     python generate_saved_makespan.py --num-tasks {task_count}")
        return False
    
    print("✅ Training data available for all configurations")
    return True


def print_summary(results):
    """Print summary of training results"""
    print(f"\n{'='*60}")
    print("📊 TRAINING SUMMARY")
    print(f"{'='*60}")
    
    total_configs = len(results)
    successful_configs = sum(1 for success in results.values() if success)
    
    for task_count, success in sorted(results.items()):
        status = "✅ SUCCESS" if success else "❌ FAILED"
        config = get_all_task_configs()[task_count]
        checkpoint_dir = f"./cp_decentralized_{task_count}tasks"
        print(f"  {task_count:3d} tasks: {status}")
        if success:
            print(f"    Checkpoint: {checkpoint_dir}")
            print(f"    Tensorboard: ./runs/DecentralizedMO_{task_count}tasks")
    
    print(f"\n📈 Overall: {successful_configs}/{total_configs} configurations successful")
    
    if successful_configs == total_configs:
        print("🎉 All models trained successfully!")
        print("\n🔧 Next steps:")
        print("  1. Evaluate models using: python test_all_configs.py")
        print("  2. Compare performance across configurations")
        print("  3. Run baselines for comparison")
    else:
        print("⚠️  Some training failed. Check logs above.")


def main():
    """Main function with command line argument support"""
    parser = argparse.ArgumentParser(
        description="Train multi-robot scheduling models for all task configurations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train all configurations sequentially with default parameters
  python train_all_configs.py
  
  # Train all configurations in parallel with custom parameters
  python train_all_configs.py --parallel --steps 200 --lr 1e-6
  
  # Train only specific configurations
  python train_all_configs.py --tasks 30 50 --steps 100
        """
    )
    
    # Configuration selection
    parser.add_argument("--tasks", type=int, nargs="+", choices=[30, 50, 100],
                       help="Specific task counts to train (default: all)")
    
    # Training mode
    parser.add_argument("--parallel", action="store_true",
                       help="Train configurations in parallel (default: sequential)")
    parser.add_argument("--max-workers", type=int, default=None,
                       help="Maximum number of parallel workers (default: auto)")
    
    # Training parameters
    parser.add_argument("--steps", type=int, default=100,
                       help="Number of training steps per configuration")
    parser.add_argument("--batch-size", type=int, default=32,
                       help="Batch size for training")
    parser.add_argument("--lr", type=float, default=5e-7,
                       help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=1e-3,
                       help="Weight decay for regularization")
    parser.add_argument("--alpha", type=float, default=0.8,
                       help="Weight for makespan objective")
    parser.add_argument("--beta", type=float, default=0.2,
                       help="Weight for workload balance objective")
    parser.add_argument("--checkpoint-interval", type=int, default=15,
                       help="Interval for saving checkpoints")
    parser.add_argument("--device", default="cpu", choices=["cpu", "cuda"],
                       help="Device to use for training")
    
    # Control options
    parser.add_argument("--stop-on-failure", action="store_true",
                       help="Stop training if any configuration fails (sequential mode only)")
    parser.add_argument("--skip-data-check", action="store_true",
                       help="Skip checking for training data availability")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be trained without actually training")
    
    args = parser.parse_args()
    
    start_time = datetime.now()
    
    print("🤖 Multi-Robot Task Scheduling Model Training")
    print("=" * 60)
    print(f"📅 Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Determine which configurations to train
    if args.tasks:
        task_counts = args.tasks
        print(f"🎯 Training models for: {task_counts} tasks")
    else:
        configs = get_all_task_configs()
        task_counts = sorted(configs.keys())
        print(f"🎯 Training models for all configurations: {task_counts} tasks")
    
    print(f"📊 Training parameters:")
    print(f"  Steps: {args.steps}")
    print(f"  Batch size: {args.batch_size}")
    print(f"  Learning rate: {args.lr}")
    print(f"  Alpha (makespan): {args.alpha}")
    print(f"  Beta (balance): {args.beta}")
    print(f"  Device: {args.device}")
    print(f"⚡ Mode: {'Parallel' if args.parallel else 'Sequential'}")
    
    if args.dry_run:
        print("\n🔍 DRY RUN - No actual training will occur")
        for task_count in task_counts:
            config = get_all_task_configs()[task_count]
            print(f"  Would train model for {task_count} tasks")
            print(f"    Data: {config.constraints_folder}")
            print(f"    Checkpoint: ./cp_decentralized_{task_count}tasks")
        return
    
    # Check data availability
    if not args.skip_data_check:
        if not check_data_availability():
            print("❌ Missing training data. Generate data first or use --skip-data-check")
            return
    
    # Ensure directories exist
    ensure_all_directories()
    
    # Train models
    if args.parallel:
        # Filter configs for parallel training
        if args.tasks:
            # For specific tasks, train them in parallel
            with mp.Pool(min(len(task_counts), args.max_workers or mp.cpu_count())) as pool:
                args_list = [(task_count, args) for task_count in task_counts]
                results = pool.starmap(train_config, args_list)
            results = dict(zip(task_counts, results))
        else:
            results = train_all_configs_parallel(args, args.max_workers)
    else:
        # Sequential training
        results = {}
        for task_count in task_counts:
            print(f"\n{'='*60}")
            print(f"Training model for {task_count} tasks")
            print(f"{'='*60}")
            success = train_config(task_count, args)
            results[task_count] = success
            
            if not success and args.stop_on_failure:
                print(f"⚠️  Stopping due to failure in {task_count} tasks configuration")
                break
    
    end_time = datetime.now()
    
    # Print summary
    print_summary(results)
    print(f"\n⏱️  Total time: {end_time - start_time}")
    print(f"🏁 Finished at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
