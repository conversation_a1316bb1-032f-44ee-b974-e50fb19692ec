#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_all_configs.py

Unified testing script for multi-robot task scheduling across different configurations:
- 2 robots, 30 tasks
- 2 robots, 50 tasks  
- 2 robots, 100 tasks

This script can evaluate models and baselines for all configurations.
"""

import os
import sys
import argparse
import subprocess
import multiprocessing as mp
from datetime import datetime
import pandas as pd
from config import ConfigManager, get_all_task_configs


def test_solver_config(task_count, solver, test_args):
    """Test a specific solver on a specific task configuration"""
    print(f"\n🧪 Testing {solver} on {task_count} tasks configuration...")
    
    config = get_all_task_configs()[task_count]
    
    # Build command for baselines evaluation
    cmd = [
        sys.executable, "baselines.py",
        "--solver", solver,
        "--num-tasks", str(task_count),
        "--path-test-data", config.constraints_folder,
        "--output-folder", f"csv_outputs_{task_count}tasks",
        "--max-instances", str(test_args.max_instances or (config.test_end_no - config.test_start_no + 1)),
        "--device", test_args.device
    ]
    
    # Add solver-specific arguments
    if solver == "ssan" and test_args.checkpoint_policy:
        checkpoint_path = test_args.checkpoint_policy
        if not checkpoint_path:
            # Auto-detect checkpoint
            checkpoint_dir = f"./cp_decentralized_{task_count}tasks"
            if os.path.exists(checkpoint_dir):
                # Find latest checkpoint
                checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
                if checkpoints:
                    latest_checkpoint = sorted(checkpoints)[-1]
                    checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)
        
        if checkpoint_path and os.path.exists(checkpoint_path):
            cmd.extend(["--checkpoint-policy", checkpoint_path])
        else:
            print(f"⚠️  No checkpoint found for {task_count} tasks, skipping SSAN")
            return False
    
    if test_args.alpha:
        cmd.extend(["--alpha", str(test_args.alpha)])
    if test_args.beta:
        cmd.extend(["--beta", str(test_args.beta)])
    
    try:
        print(f"📝 Command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ Successfully tested {solver} on {task_count} tasks")
        if result.stdout:
            print(f"📊 Test output:\n{result.stdout[-300:]}")  # Last 300 chars
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to test {solver} on {task_count} tasks")
        print(f"Error: {e.stderr}")
        return False


def test_all_configs_sequential(solvers, test_args):
    """Test all configurations sequentially"""
    print("🔄 Testing models sequentially...")
    
    configs = get_all_task_configs()
    results = {}
    
    for task_count in sorted(configs.keys()):
        results[task_count] = {}
        print(f"\n{'='*60}")
        print(f"Testing {task_count} tasks configuration")
        print(f"{'='*60}")
        
        for solver in solvers:
            success = test_solver_config(task_count, solver, test_args)
            results[task_count][solver] = success
            
            if not success and test_args.stop_on_failure:
                print(f"⚠️  Stopping due to failure in {solver} on {task_count} tasks")
                return results
    
    return results


def test_all_configs_parallel(solvers, test_args, max_workers=None):
    """Test all configurations in parallel"""
    print("⚡ Testing models in parallel...")
    
    configs = get_all_task_configs()
    task_counts = sorted(configs.keys())
    
    # Create all combinations of (task_count, solver)
    test_combinations = [(task_count, solver, test_args) 
                        for task_count in task_counts 
                        for solver in solvers]
    
    if max_workers is None:
        max_workers = min(len(test_combinations), mp.cpu_count())
    
    print(f"Using {max_workers} parallel workers for {len(test_combinations)} test combinations")
    
    with mp.Pool(max_workers) as pool:
        # Run in parallel
        results_flat = pool.starmap(test_solver_config, test_combinations)
    
    # Reorganize results
    results = {}
    idx = 0
    for task_count in task_counts:
        results[task_count] = {}
        for solver in solvers:
            results[task_count][solver] = results_flat[idx]
            idx += 1
    
    return results


def check_test_data_availability():
    """Check if test data is available for all configurations"""
    print("🔍 Checking test data availability...")
    
    configs = get_all_task_configs()
    missing_data = []
    
    for task_count, config in configs.items():
        # Check if constraint files exist
        constraints_dir = config.constraints_folder
        if not os.path.exists(constraints_dir):
            missing_data.append(task_count)
            continue
        
        # Check if we have test instances
        test_start = config.test_start_no
        test_files = [f"{i:05d}_dur.txt" for i in range(test_start, min(test_start + 5, config.test_end_no + 1))]
        existing_files = [f for f in test_files if os.path.exists(os.path.join(constraints_dir, f))]
        
        if len(existing_files) < 2:  # Need at least 2 files for basic testing
            missing_data.append(task_count)
    
    if missing_data:
        print(f"⚠️  Missing test data for configurations: {missing_data}")
        print("   Generate data first using:")
        for task_count in missing_data:
            print(f"     python generate_saved_makespan.py --num-tasks {task_count}")
        return False
    
    print("✅ Test data available for all configurations")
    return True


def check_trained_models():
    """Check if trained models are available"""
    print("🔍 Checking trained models availability...")
    
    configs = get_all_task_configs()
    missing_models = []
    
    for task_count in configs.keys():
        checkpoint_dir = f"./cp_decentralized_{task_count}tasks"
        if not os.path.exists(checkpoint_dir):
            missing_models.append(task_count)
            continue
        
        # Check if we have checkpoint files
        checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
        if not checkpoints:
            missing_models.append(task_count)
    
    if missing_models:
        print(f"⚠️  Missing trained models for configurations: {missing_models}")
        print("   Train models first using:")
        print("     python train_all_configs.py")
        return False
    
    print("✅ Trained models available for all configurations")
    return True


def analyze_results(results):
    """Analyze and compare results across configurations"""
    print(f"\n{'='*60}")
    print("📊 RESULTS ANALYSIS")
    print(f"{'='*60}")
    
    # Create summary table
    summary_data = []
    for task_count, solver_results in results.items():
        for solver, success in solver_results.items():
            summary_data.append({
                'Tasks': task_count,
                'Solver': solver,
                'Success': '✅' if success else '❌'
            })
    
    if summary_data:
        df = pd.DataFrame(summary_data)
        print("\n📋 Test Results Summary:")
        print(df.pivot(index='Solver', columns='Tasks', values='Success').to_string())
    
    # Count successes
    total_tests = sum(len(solver_results) for solver_results in results.values())
    successful_tests = sum(sum(1 for success in solver_results.values() if success) 
                          for solver_results in results.values())
    
    print(f"\n📈 Overall: {successful_tests}/{total_tests} tests successful")
    
    # Check for CSV outputs
    print(f"\n📁 Generated CSV files:")
    for task_count in results.keys():
        csv_dir = f"csv_outputs_{task_count}tasks"
        if os.path.exists(csv_dir):
            csv_files = [f for f in os.listdir(csv_dir) if f.endswith('.csv')]
            print(f"  {task_count} tasks: {len(csv_files)} CSV files in {csv_dir}")
        else:
            print(f"  {task_count} tasks: No CSV output directory found")


def print_summary(results):
    """Print summary of testing results"""
    print(f"\n{'='*60}")
    print("📊 TESTING SUMMARY")
    print(f"{'='*60}")
    
    for task_count, solver_results in sorted(results.items()):
        print(f"\n🎯 {task_count} tasks configuration:")
        for solver, success in solver_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"  {solver:15s}: {status}")
    
    # Overall statistics
    total_tests = sum(len(solver_results) for solver_results in results.values())
    successful_tests = sum(sum(1 for success in solver_results.values() if success) 
                          for solver_results in results.values())
    
    print(f"\n📈 Overall: {successful_tests}/{total_tests} tests successful")
    
    if successful_tests == total_tests:
        print("🎉 All tests completed successfully!")
    else:
        print("⚠️  Some tests failed. Check logs above.")


def main():
    """Main function with command line argument support"""
    parser = argparse.ArgumentParser(
        description="Test multi-robot scheduling models across all task configurations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test all solvers on all configurations
  python test_all_configs.py
  
  # Test only specific solvers in parallel
  python test_all_configs.py --solvers ssan gurobi --parallel
  
  # Test only specific configurations
  python test_all_configs.py --tasks 30 50 --max-instances 50
        """
    )
    
    # Configuration selection
    parser.add_argument("--tasks", type=int, nargs="+", choices=[30, 50, 100],
                       help="Specific task counts to test (default: all)")
    parser.add_argument("--solvers", nargs="+", 
                       choices=["edf", "tercio", "random", "gurobi", "ssan", 
                               "workload_balance", "multi_objective", "balance_heuristic"],
                       default=["edf", "tercio", "random", "gurobi", "ssan"],
                       help="Solvers to test (default: edf, tercio, random, gurobi, ssan)")
    
    # Testing mode
    parser.add_argument("--parallel", action="store_true",
                       help="Run tests in parallel (default: sequential)")
    parser.add_argument("--max-workers", type=int, default=None,
                       help="Maximum number of parallel workers (default: auto)")
    
    # Testing parameters
    parser.add_argument("--max-instances", type=int, default=None,
                       help="Maximum instances to test per configuration (default: all test instances)")
    parser.add_argument("--checkpoint-policy", default=None,
                       help="Path to SSAN checkpoint (auto-detected if not specified)")
    parser.add_argument("--device", default="cpu", choices=["cpu", "cuda"],
                       help="Device to use for testing")
    parser.add_argument("--alpha", type=float, default=0.5,
                       help="Weight for makespan objective in multi-objective solvers")
    parser.add_argument("--beta", type=float, default=0.5,
                       help="Weight for workload balance objective in multi-objective solvers")
    
    # Control options
    parser.add_argument("--stop-on-failure", action="store_true",
                       help="Stop testing if any test fails (sequential mode only)")
    parser.add_argument("--skip-data-check", action="store_true",
                       help="Skip checking for test data availability")
    parser.add_argument("--skip-model-check", action="store_true",
                       help="Skip checking for trained models (for non-SSAN solvers)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be tested without actually testing")
    
    args = parser.parse_args()
    
    start_time = datetime.now()
    
    print("🤖 Multi-Robot Task Scheduling Model Testing")
    print("=" * 60)
    print(f"📅 Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Determine which configurations to test
    if args.tasks:
        task_counts = args.tasks
        print(f"🎯 Testing configurations: {task_counts} tasks")
    else:
        configs = get_all_task_configs()
        task_counts = sorted(configs.keys())
        print(f"🎯 Testing all configurations: {task_counts} tasks")
    
    print(f"🔧 Testing solvers: {args.solvers}")
    print(f"📊 Max instances per config: {args.max_instances or 'all'}")
    print(f"⚡ Mode: {'Parallel' if args.parallel else 'Sequential'}")
    
    if args.dry_run:
        print("\n🔍 DRY RUN - No actual testing will occur")
        for task_count in task_counts:
            config = get_all_task_configs()[task_count]
            print(f"  Would test {task_count} tasks:")
            print(f"    Data: {config.constraints_folder}")
            print(f"    Solvers: {args.solvers}")
        return
    
    # Check data and model availability
    if not args.skip_data_check:
        if not check_test_data_availability():
            print("❌ Missing test data. Generate data first or use --skip-data-check")
            return
    
    if "ssan" in args.solvers and not args.skip_model_check:
        if not check_trained_models():
            print("❌ Missing trained models. Train models first or use --skip-model-check")
            return
    
    # Run tests
    if args.parallel:
        results = test_all_configs_parallel(args.solvers, args, args.max_workers)
    else:
        results = test_all_configs_sequential(args.solvers, args)
    
    end_time = datetime.now()
    
    # Print summary and analysis
    print_summary(results)
    analyze_results(results)
    print(f"\n⏱️  Total time: {end_time - start_time}")
    print(f"🏁 Finished at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
